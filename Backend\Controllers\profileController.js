import { query } from "../db.js"
import multer from "multer"
import path from "path"
import fs from "fs"

const storage = multer.diskStorage({
  destination: (_, __, cb) => {
    const uploadDir = "Backend/uploads/profiles"
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true })
    }
    cb(null, uploadDir)
  },
  filename: (_, file, cb) => {
    cb(null, Date.now() + path.extname(file.originalname))
  },
})

export const upload = multer({ storage })

export const addProfile = (req, res) => {
  const {
    account_id,
    full_name,
    designation,
    department,
    company_name,
    office_location,
    instagram,
    facebook,
    work_email,
    office_number,
    linkedin_url,
    personal_email,
    mobile_number,
    date_of_birth,
    marital_status,
    children,
    hobbies,
    clubs,
    education,
    personality_traits,
    career_history,
    key_responsibilities,
    kpis,
    decision_role,
    reports_to,
    influenced_by,
    has_influence_over,
    authority_level,
  } = req.body

  console.log("📥 Incoming profile data:", { account_id, full_name, designation })

  if (!full_name || !account_id) {
    console.error("❌ Missing required fields for profile")
    return res.status(400).json({ error: "Missing required fields" })
  }

  const profile_photo = req.file?.filename || null

  const authority = Array.isArray(authority_level) ? authority_level.join(",") : authority_level

  const sql = `
    INSERT INTO client_profiles (
      account_id, full_name, designation, department, company_name, 
      office_location, instagram, facebook, work_email, office_number, 
      linkedin_url, personal_email, mobile_number, date_of_birth, 
      marital_status, children, hobbies, clubs, education, 
      personality_traits, profile_photo, career_history, key_responsibilities, 
      kpis, decision_role, reports_to, influenced_by, has_influence_over, 
      authority_level
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `

  query(
    sql,
    [
      account_id,
      full_name,
      designation,
      department,
      company_name,
      office_location,
      instagram,
      facebook,
      work_email,
      office_number,
      linkedin_url,
      personal_email,
      mobile_number,
      date_of_birth,
      marital_status,
      children,
      hobbies,
      clubs,
      education,
      personality_traits,
      profile_photo,
      career_history,
      key_responsibilities,
      kpis,
      decision_role,
      reports_to,
      influenced_by,
      has_influence_over,
      authority,
    ],
    (err, result) => {
      if (err) {
        console.error("❌ Profile insert failed:", err.message)
        return res.status(500).json({ error: err.message })
      }

      console.log("✅ Profile added successfully:", result)
      res.status(201).json({
        message: "✅ Profile added successfully",
        id: result.insertId,
      })
    },
  )
}

export const getProfilesByAccount = (req, res) => {
  const { account_id } = req.params

  console.log("📥 Fetching profiles for account:", account_id)

  query(`SELECT * FROM client_profiles WHERE account_id = ?`, [account_id], (err, results) => {
    if (err) {
      console.error("❌ Error fetching profiles:", err.message)
      return res.status(500).json({ error: err.message })
    }

    console.log(`✅ Found ${results.length} profiles for account ${account_id}`)
    res.json(results)
  })
}

export const getProfileById = (req, res) => {
  const { id } = req.params

  console.log("📥 Fetching profile:", id)

  query(`SELECT * FROM client_profiles WHERE id = ?`, [id], (err, results) => {
    if (err) {
      console.error("❌ Error fetching profile:", err.message)
      return res.status(500).json({ error: err.message })
    }

    if (results.length === 0) {
      return res.status(404).json({ error: "Profile not found" })
    }

    console.log("✅ Profile found")
    res.json(results[0])
  })
}

export const deleteProfile = (req, res) => {
  const { id } = req.params

  console.log("📥 Deleting profile:", id)

  query(`DELETE FROM client_profiles WHERE id = ?`, [id], (err) => {
    if (err) {
      console.error("❌ Error deleting profile:", err.message)
      return res.status(500).json({ error: err.message })
    }

    console.log("✅ Profile deleted successfully")
    res.json({ message: "🗑️ Profile deleted" })
  })
}

export const updateProfile = (req, res) => {
  const { id } = req.params
  const {
    full_name,
    designation,
    department,
    company_name,
    office_location,
    instagram,
    facebook,
    work_email,
    office_number,
    linkedin_url,
    personal_email,
    mobile_number,
    date_of_birth,
    marital_status,
    children,
    hobbies,
    clubs,
    education,
    personality_traits,
    career_history,
    key_responsibilities,
    kpis,
    decision_role,
    reports_to,
    influenced_by,
    has_influence_over,
    authority_level,
  } = req.body

  console.log("📥 Updating profile:", id, { full_name, designation })

  if (!full_name) {
    console.error("❌ Missing required fields for profile update")
    return res.status(400).json({ error: "Missing required fields" })
  }

  const profile_photo = req.file?.filename || null
  const authority = Array.isArray(authority_level) ? authority_level.join(",") : authority_level

  let sql = `
    UPDATE client_profiles SET 
      full_name = ?, designation = ?, department = ?, company_name = ?, 
      office_location = ?, instagram = ?, facebook = ?, work_email = ?, 
      office_number = ?, linkedin_url = ?, personal_email = ?, mobile_number = ?, 
      date_of_birth = ?, marital_status = ?, children = ?, hobbies = ?, 
      clubs = ?, education = ?, personality_traits = ?, career_history = ?, 
      key_responsibilities = ?, kpis = ?, decision_role = ?, reports_to = ?, 
      influenced_by = ?, has_influence_over = ?, authority_level = ?
  `

  let queryParams = [
    full_name,
    designation,
    department,
    company_name,
    office_location,
    instagram,
    facebook,
    work_email,
    office_number,
    linkedin_url,
    personal_email,
    mobile_number,
    date_of_birth,
    marital_status,
    children,
    hobbies,
    clubs,
    education,
    personality_traits,
    career_history,
    key_responsibilities,
    kpis,
    decision_role,
    reports_to,
    influenced_by,
    has_influence_over,
    authority,
  ]

  if (profile_photo) {
    sql += ", profile_photo = ?"
    queryParams.push(profile_photo)
  }

  sql += " WHERE id = ?"
  queryParams.push(id)

  query(sql, queryParams, (err, result) => {
    if (err) {
      console.error("❌ Profile update failed:", err.message)
      return res.status(500).json({ error: err.message })
    }

    if (result.affectedRows === 0) {
      return res.status(404).json({ error: "Profile not found" })
    }

    console.log("✅ Profile updated successfully:", result)
    res.json({ message: "✅ Profile updated successfully" })
  })
}
