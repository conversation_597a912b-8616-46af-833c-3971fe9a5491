import { useState, useEffect } from "react"
import { DndContext, DragOverlay, MouseSensor, TouchSensor, useSensor, useSensors, pointerWithin } from "@dnd-kit/core"
import DroppableNode from "./DroppableNode"
import ProfileNodeContent from "./ProfileNodeContent"

const MindMapDiagram = ({
  profiles,
  departments = [],
  setDepartments,
  nodePlacements = [],
  setNodePlacements,
  onSave,
}) => {
  const [activeId, setActiveId] = useState(null)
  const [activeProfile, setActiveProfile] = useState(null)
  const [newDepartment, setNewDepartment] = useState("")

  useEffect(() => {
    console.log("MindMapDiagram rendered with:", {
      profilesCount: profiles?.length || 0,
      departments: departments || [],
      nodePlacements: nodePlacements || [],
    })
  }, [profiles, departments, nodePlacements])

  const mouseSensor = useSensor(MouseSensor, {
    activationConstraint: {
      distance: 5,
    },
  })

  const touchSensor = useSensor(TouchSensor, {
    activationConstraint: {
      delay: 100,
      tolerance: 5,
    },
  })

  const sensors = useSensors(mouseSensor, touchSensor)

  const handleDragStart = (event) => {
    const { active } = event
    console.log("Drag start:", active)
    setActiveId(active.id)

    if (active.data.current?.type === "profile") {
      setActiveProfile(active.data.current.profile)
    }
  }

  const handleDragEnd = (event) => {
    const { active, over } = event
    console.log("Drag end:", { active, over })

    if (over && active.data.current?.type === "profile") {
      const profileId = active.data.current.profile.id
      const nodeId = over.id

      if (nodeId.startsWith("department-")) {
        const departmentIndex = Number.parseInt(nodeId.split("-")[1])
        console.log(`Dropping profile ${profileId} onto department index ${departmentIndex}`)

        const updatedPlacements = [...(nodePlacements || [])]

        const existingPlacementIndex = updatedPlacements.findIndex((p) => p.profile_id === profileId)

        if (existingPlacementIndex !== -1) {
          updatedPlacements[existingPlacementIndex].department_index = departmentIndex
          console.log("Updated existing placement")
        } else {
          updatedPlacements.push({
            profile_id: profileId,
            department_index: departmentIndex,
          })
          console.log("Added new placement")
        }

        setNodePlacements(updatedPlacements)

        console.log("Saving with:", { departments, updatedPlacements })
        onSave(departments || [], updatedPlacements)
      }
    }

    setActiveId(null)
    setActiveProfile(null)
  }

  const handleDragOver = (event) => {
    console.log("Drag over:", event)
  }

  const handleAddDepartment = () => {
    if (newDepartment.trim() !== "") {
      const updatedDepartments = [...(departments || []), newDepartment.trim()]
      console.log("Adding department:", newDepartment.trim(), "New departments:", updatedDepartments)
      setDepartments(updatedDepartments)
      setNewDepartment("")
      onSave(updatedDepartments, nodePlacements || [])
    }
  }

  const handleRemoveDepartment = (index) => {
    console.log("Removing department at index:", index)
    const updatedDepartments = [...(departments || [])]
    updatedDepartments.splice(index, 1)

    const updatedPlacements = (nodePlacements || []).filter((p) => p.department_index !== index)

    updatedPlacements.forEach((p) => {
      if (p.department_index > index) {
        p.department_index -= 1
      }
    })

    setDepartments(updatedDepartments)
    setNodePlacements(updatedPlacements)
    onSave(updatedDepartments, updatedPlacements)
  }

  const getProfilesForDepartment = (departmentIndex) => {
    if (!nodePlacements || !profiles) return []

    const placementsForDepartment = nodePlacements.filter((p) => p.department_index === departmentIndex)
    return placementsForDepartment
      .map((p) => {
        return profiles.find((profile) => profile.id === p.profile_id)
      })
      .filter(Boolean)
  }

  const handleRemoveProfileFromDepartment = (profileId) => {
    console.log("Removing profile from department:", profileId)
    const updatedPlacements = (nodePlacements || []).filter((p) => p.profile_id !== profileId)
    setNodePlacements(updatedPlacements)
    onSave(departments || [], updatedPlacements)
  }

  const departmentsArray = Array.isArray(departments) ? departments : []

  return (
    <div className="w-full">
      <div className="mb-6 flex">
        <input
          type="text"
          value={newDepartment}
          onChange={(e) => setNewDepartment(e.target.value)}
          placeholder="Add new department"
          className="flex-1 border border-gray-300 rounded-l-lg p-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
        <button
          onClick={handleAddDepartment}
          className="bg-blue-500 text-white px-4 py-2 rounded-r-lg hover:bg-blue-600 transition-colors"
        >
          Add Department
        </button>
      </div>

      <DndContext
        sensors={sensors}
        collisionDetection={pointerWithin} 
        onDragStart={handleDragStart}
        onDragOver={handleDragOver}
        onDragEnd={handleDragEnd}
      >
        <div className="relative w-full overflow-x-auto pb-6">
          <div className="min-w-[600px] min-h-[400px]">
            <div className="flex justify-center mb-6">
              <div className="bg-indigo-600 text-white rounded-full px-6 py-3 text-lg font-bold shadow-lg">
                Opportunity Map
              </div>
            </div>

            <div className="grid grid-cols-2 gap-6">
              {departmentsArray.map((department, index) => (
                <div key={index} className="flex flex-col">
                  <div className="flex items-center mb-3">
                    <div className="flex-1 relative">
                      <div className="absolute inset-0 flex items-center">
                        <div className="h-0.5 w-full bg-gray-300"></div>
                      </div>
                      <div className="relative flex justify-center">
                        <div className="flex items-center">
                          <DroppableNode
                            id={`department-${index}`}
                            className="bg-white border-2 border-gray-300 rounded-full px-4 py-2 font-medium z-10 text-sm"
                          >
                            {department}
                          </DroppableNode>
                          <button
                            onClick={() => handleRemoveDepartment(index)}
                            className="ml-2 text-red-500 hover:text-red-700 z-10"
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              className="h-4 w-4"
                              viewBox="0 0 20 20"
                              fill="currentColor"
                            >
                              <path
                                fillRule="evenodd"
                                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                                clipRule="evenodd"
                              />
                            </svg>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="pl-6 space-y-2">
                    {getProfilesForDepartment(index).map((profile) => (
                      <div key={profile.id} className="relative">
                        <div className="absolute top-1/2 -left-6 w-6 h-0.5 bg-gray-300"></div>
                        <ProfileNodeContent
                          profile={profile}
                          onRemove={() => handleRemoveProfileFromDepartment(profile.id)}
                        />
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        <DragOverlay>
          {activeId && activeProfile ? (
            <div className="bg-white rounded-lg shadow-lg p-3 border border-blue-300 w-48">
              <div className="flex items-center space-x-3">
                {activeProfile.profile_photo ? (
                  <img
                    src={`http://localhost:5000/uploads/profiles/${activeProfile.profile_photo}`}
                    alt={activeProfile.full_name}
                    className="w-10 h-10 rounded-full object-cover"
                  />
                ) : (
                  <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-6 w-6 text-gray-400"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                      />
                    </svg>
                  </div>
                )}
                <div>
                  <h3 className="font-medium text-sm text-gray-800">{activeProfile.full_name}</h3>
                  <p className="text-xs text-gray-500">{activeProfile.designation}</p>
                </div>
              </div>
            </div>
          ) : null}
        </DragOverlay>
      </DndContext>
    </div>
  )
}

export default MindMapDiagram
