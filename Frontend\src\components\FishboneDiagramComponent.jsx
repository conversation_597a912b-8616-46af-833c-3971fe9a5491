import { useState, useEffect } from "react"
import { DndContext, DragOverlay, MouseSensor, TouchSensor, useSensor, useSensors, pointerWithin } from "@dnd-kit/core"
import DroppableNode from "./DroppableNode"
import ProfileNodeContent from "./ProfileNodeContent"

const MindMapDiagram = ({
  profiles,
  departments = [],
  setDepartments,
  nodePlacements = [],
  setNodePlacements,
  onSave,
}) => {
  const [activeId, setActiveId] = useState(null)
  const [activeProfile, setActiveProfile] = useState(null)
  const [newDepartment, setNewDepartment] = useState("")

  useEffect(() => {
    console.log("MindMapDiagram rendered with:", {
      profilesCount: profiles?.length || 0,
      departments: departments || [],
      nodePlacements: nodePlacements || [],
    })
  }, [profiles, departments, nodePlacements])

  const mouseSensor = useSensor(MouseSensor, {
    activationConstraint: {
      distance: 5,
    },
  })

  const touchSensor = useSensor(TouchSensor, {
    activationConstraint: {
      delay: 100,
      tolerance: 5,
    },
  })

  const sensors = useSensors(mouseSensor, touchSensor)

  const handleDragStart = (event) => {
    const { active } = event
    console.log("Drag start:", active)
    setActiveId(active.id)

    if (active.data.current?.type === "profile") {
      setActiveProfile(active.data.current.profile)
    }
  }

  const handleDragEnd = (event) => {
    const { active, over } = event
    console.log("Drag end:", { active, over })

    if (over && active.data.current?.type === "profile") {
      const profileId = active.data.current.profile.id
      const nodeId = over.id

      if (nodeId.startsWith("department-")) {
        const departmentIndex = Number.parseInt(nodeId.split("-")[1])
        console.log(`Dropping profile ${profileId} onto department index ${departmentIndex}`)

        const updatedPlacements = [...(nodePlacements || [])]

        const existingPlacementIndex = updatedPlacements.findIndex((p) => p.profile_id === profileId)

        if (existingPlacementIndex !== -1) {
          updatedPlacements[existingPlacementIndex].department_index = departmentIndex
          console.log("Updated existing placement")
        } else {
          updatedPlacements.push({
            profile_id: profileId,
            department_index: departmentIndex,
          })
          console.log("Added new placement")
        }

        setNodePlacements(updatedPlacements)

        console.log("Saving with:", { departments, updatedPlacements })
        onSave(departments || [], updatedPlacements)
      }
    }

    setActiveId(null)
    setActiveProfile(null)
  }

  const handleDragOver = (event) => {
    console.log("Drag over:", event)
  }

  const handleAddDepartment = () => {
    if (newDepartment.trim() !== "") {
      const updatedDepartments = [...(departments || []), newDepartment.trim()]
      console.log("Adding department:", newDepartment.trim(), "New departments:", updatedDepartments)
      setDepartments(updatedDepartments)
      setNewDepartment("")
      onSave(updatedDepartments, nodePlacements || [])
    }
  }

  const handleRemoveDepartment = (index) => {
    console.log("Removing department at index:", index)
    const updatedDepartments = [...(departments || [])]
    updatedDepartments.splice(index, 1)

    const updatedPlacements = (nodePlacements || []).filter((p) => p.department_index !== index)

    updatedPlacements.forEach((p) => {
      if (p.department_index > index) {
        p.department_index -= 1
      }
    })

    setDepartments(updatedDepartments)
    setNodePlacements(updatedPlacements)
    onSave(updatedDepartments, updatedPlacements)
  }

  const getProfilesForDepartment = (departmentIndex) => {
    if (!nodePlacements || !profiles) return []

    const placementsForDepartment = nodePlacements.filter((p) => p.department_index === departmentIndex)
    return placementsForDepartment
      .map((p) => {
        return profiles.find((profile) => profile.id === p.profile_id)
      })
      .filter(Boolean)
  }

  const handleRemoveProfileFromDepartment = (profileId) => {
    console.log("Removing profile from department:", profileId)
    const updatedPlacements = (nodePlacements || []).filter((p) => p.profile_id !== profileId)
    setNodePlacements(updatedPlacements)
    onSave(departments || [], updatedPlacements)
  }

  const departmentsArray = Array.isArray(departments) ? departments : []

  return (
    <div className="w-full">
      <div className="mb-6 flex">
        <input
          type="text"
          value={newDepartment}
          onChange={(e) => setNewDepartment(e.target.value)}
          placeholder="Add new department"
          className="flex-1 border border-gray-300 rounded-l-lg p-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
        <button
          onClick={handleAddDepartment}
          className="bg-blue-500 text-white px-4 py-2 rounded-r-lg hover:bg-blue-600 transition-colors"
        >
          Add Department
        </button>
      </div>

      <DndContext
        sensors={sensors}
        collisionDetection={pointerWithin}
        onDragStart={handleDragStart}
        onDragOver={handleDragOver}
        onDragEnd={handleDragEnd}
      >
        <div className="relative w-full overflow-x-auto pb-6">
          <div className="min-w-[1400px] min-h-[700px] relative">
            {/* Central Spine */}
            <div className="absolute top-1/2 left-16 right-48 h-1 bg-gray-600 transform -translate-y-0.5 z-5"></div>

            {/* Central Node - at the right end of spine */}
            <div className="absolute top-1/2 right-16 transform -translate-y-1/2 z-20">
              <div className="bg-indigo-600 text-white rounded-lg px-8 py-4 text-xl font-bold shadow-lg border-4 border-white">
                Opportunity Map
              </div>
            </div>

            {/* Department Branches */}
            {departmentsArray.map((department, index) => {
              const isAbove = index % 2 === 0; // Alternate above/below
              const branchLength = 150; // Length of the angled branch

              // Calculate position along the spine
              const totalDepartments = departmentsArray.length;
              const spineStart = 80; // Start position from left
              const spineEnd = 1100; // End position (before central node)
              const spineLength = spineEnd - spineStart;
              const departmentX = spineStart + (spineLength / (totalDepartments + 1)) * (index + 1);

              // Calculate branch end position at 45-degree angle
              const angle = isAbove ? -45 : 45; // degrees
              const angleRad = (angle * Math.PI) / 180;
              const branchEndX = departmentX + branchLength * Math.cos(angleRad);
              const branchEndY = 350 + branchLength * Math.sin(angleRad); // 350 is center Y

              const profiles = getProfilesForDepartment(index);

              return (
                <div key={index}>
                  {/* Angled Branch Line */}
                  <svg
                    className="absolute top-0 left-0 w-full h-full pointer-events-none z-10"
                    style={{ overflow: 'visible' }}
                  >
                    <line
                      x1={departmentX}
                      y1={350}
                      x2={branchEndX}
                      y2={branchEndY}
                      stroke="#4B5563"
                      strokeWidth="4"
                    />
                  </svg>

                  {/* Department Node */}
                  <div
                    className="absolute z-20"
                    style={{
                      left: `${branchEndX - 70}px`,
                      top: `${branchEndY - 25}px`
                    }}
                  >
                    <div className="flex items-center">
                      <DroppableNode
                        id={`department-${index}`}
                        className="bg-white border-3 border-blue-500 rounded-lg px-6 py-3 font-bold text-blue-700 shadow-lg hover:shadow-xl transition-shadow text-sm whitespace-nowrap"
                      >
                        {department}
                      </DroppableNode>
                      <button
                        onClick={() => handleRemoveDepartment(index)}
                        className="ml-2 text-red-500 hover:text-red-700 z-30 bg-white rounded-full p-1 shadow-md"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-4 w-4"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fillRule="evenodd"
                            d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </button>
                    </div>
                  </div>

                  {/* Profile Sub-branches */}
                  {profiles.map((profile, profileIndex) => {
                    const profileBranchLength = 100;

                    // Profile branches extend perpendicular to the department branch
                    // Calculate the perpendicular direction
                    const perpAngle = isAbove ? angleRad + Math.PI/2 : angleRad - Math.PI/2;

                    // Start from the department node position
                    const profileStartX = branchEndX;
                    const profileStartY = branchEndY;

                    // Calculate profile end position along the perpendicular
                    const profileEndX = profileStartX + (profileBranchLength * (profileIndex + 1)) * Math.cos(perpAngle);
                    const profileEndY = profileStartY + (profileBranchLength * (profileIndex + 1)) * Math.sin(perpAngle);

                    return (
                      <div key={profile.id}>
                        {/* Profile Branch Line */}
                        <svg
                          className="absolute top-0 left-0 w-full h-full pointer-events-none z-10"
                          style={{ overflow: 'visible' }}
                        >
                          <line
                            x1={profileStartX}
                            y1={profileStartY}
                            x2={profileEndX}
                            y2={profileEndY}
                            stroke="#9CA3AF"
                            strokeWidth="2"
                          />
                        </svg>

                        {/* Profile Node */}
                        <div
                          className="absolute z-20"
                          style={{
                            left: `${profileEndX - 80}px`,
                            top: `${profileEndY - 20}px`
                          }}
                        >
                          <ProfileNodeContent
                            profile={profile}
                            onRemove={() => handleRemoveProfileFromDepartment(profile.id)}
                          />
                        </div>
                      </div>
                    );
                  })}
                </div>
              );
            })}
          </div>
        </div>

        <DragOverlay>
          {activeId && activeProfile ? (
            <div className="bg-white rounded-lg shadow-lg p-3 border border-blue-300 w-48">
              <div className="flex items-center space-x-3">
                {activeProfile.profile_photo ? (
                  <img
                    src={`http://localhost:5000/uploads/profiles/${activeProfile.profile_photo}`}
                    alt={activeProfile.full_name}
                    className="w-10 h-10 rounded-full object-cover"
                  />
                ) : (
                  <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-6 w-6 text-gray-400"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                      />
                    </svg>
                  </div>
                )}
                <div>
                  <h3 className="font-medium text-sm text-gray-800">{activeProfile.full_name}</h3>
                  <p className="text-xs text-gray-500">{activeProfile.designation}</p>
                </div>
              </div>
            </div>
          ) : null}
        </DragOverlay>
      </DndContext>
    </div>
  )
}

export default MindMapDiagram
