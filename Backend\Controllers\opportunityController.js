import { query } from "../db.js"

export const addOpportunity = (req, res) => {
  const { name, description, account_id, sector } = req.body

  console.log("📥 Incoming opportunity data:", { name, description, account_id, sector })

  const sql = `INSERT INTO opportunities (name, description, account_id, sector) VALUES (?, ?, ?, ?)`
  query(sql, [name, description, account_id, sector], (err, result) => {
    if (err) {
      console.error("❌ Opportunity insert failed:", err.message)
      return res.status(500).json({ error: err.message })
    }
    res.json({
      message: "✅ Opportunity added successfully",
      id: result.insertId,
    })
  })
}

export const getOpportunitiesByAccount = (req, res) => {
  const { account_id } = req.params

  query(`SELECT * FROM opportunities WHERE account_id = ?`, [account_id], (err, results) => {
    if (err) return res.status(500).json({ error: err.message })
    res.json(results)
  })
}

export const deleteOpportunity = (req, res) => {
  const { id } = req.params

  query(`DELETE FROM opportunities WHERE id = ?`, [id], (err) => {
    if (err) return res.status(500).json({ error: err.message })
    res.json({ message: "🗑️ Opportunity deleted" })
  })
}
