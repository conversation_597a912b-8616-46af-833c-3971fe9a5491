CREATE DATABASE IF NOT EXISTS mnc;
USE mnc;

CREATE TABLE IF NOT EXISTS admins (
  id INT AUTO_INCREMENT PRIMARY KEY,
  username VARCHAR(50) NOT NULL UNIQUE,
  password VARCHAR(255) NOT NULL,
  role ENUM('superadmin', 'BFS<PERSON>', 'Defense', 'Telco', 'Enterprise') NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS client_accounts (
  id INT AUTO_INCREMENT PRIMARY KEY,
  admin_id INT NOT NULL,
  sector ENUM('BFSI', 'Defense', 'Telco', 'Enterprise') NOT NULL,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  address VARCHAR(255),
  type VARCHAR(50),
  image_path VARCHAR(255),
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (admin_id) REFERENCES admins(id) ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS opportunities (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name <PERSON><PERSON>HA<PERSON>(100) NOT NULL,
  description TEXT,
  account_id INT NOT NULL,
  sector ENUM('BFSI', 'Defense', 'Telco', 'Enterprise') NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (account_id) REFERENCES client_accounts(id) ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS client_profiles (
  id INT AUTO_INCREMENT PRIMARY KEY,
  account_id INT NOT NULL,
  full_name VARCHAR(255) NOT NULL,
  designation VARCHAR(255),
  department VARCHAR(255),
  company_name VARCHAR(255),
  office_location VARCHAR(255),
  instagram VARCHAR(255),
  facebook VARCHAR(255),
  work_email VARCHAR(255),
  office_number VARCHAR(50),
  linkedin_url VARCHAR(255),
  personal_email VARCHAR(255),
  mobile_number VARCHAR(50),
  date_of_birth DATE,
  marital_status ENUM('Single', 'Married', 'Divorced'),
  children VARCHAR(255),
  hobbies VARCHAR(255),
  clubs VARCHAR(255),
  education VARCHAR(255),
  personality_traits VARCHAR(255),
  profile_photo VARCHAR(255),
  career_history TEXT,
  key_responsibilities TEXT,
  kpis TEXT,
  decision_role ENUM('Decision Maker', 'Influencer', 'User', 'Gatekeeper'),
  reports_to VARCHAR(255),
  influenced_by VARCHAR(255),
  has_influence_over VARCHAR(255),
  authority_level SET('Strategic', 'Technical', 'Budget', 'Advisory'),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (account_id) REFERENCES client_accounts(id) ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS fishbone_diagrams (
  id INT AUTO_INCREMENT PRIMARY KEY,
  opportunity_id INT NOT NULL,
  diagram_data TEXT, 
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (opportunity_id) REFERENCES opportunities(id) ON DELETE CASCADE
);
