import { query } from '../db.js';
import multer from 'multer';
import path from 'path';
import fs from 'fs';

const storage = multer.diskStorage({
  destination: function (_, __, cb) {
    const uploadDir = 'Backend/uploads';
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (_, file, cb) {
    cb(null, Date.now() + path.extname(file.originalname));
  }
});

export const upload = multer({ storage });

export const addAccount = (req, res) => {
  const { name, description, address, sector, type } = req.body;
  const admin_id = req.body.admin_id;
  const image = req.file?.filename || null;

  console.log("Incoming account data:", { admin_id, name, description, address, sector, type, image });

  const sql = `INSERT INTO client_accounts (admin_id, name, description, address, sector, type, image_path) VALUES (?, ?, ?, ?, ?, ?, ?)`;
  query(sql, [admin_id, name, description, address, sector, type, image], (err, result) => {
    if (err) {
      console.error("Account insert failed:", err.message);
      return res.status(500).json({ error: err.message });
    }
    res.json({ message: 'Account added successfully' });
  });
};

export const getAccountsByAdmin = (req, res) => {
  const { admin_id } = req.params;
  query(`SELECT * FROM client_accounts WHERE admin_id = ?`, [admin_id], (err, results) => {
    if (err) return res.status(500).json({ error: err.message });
    res.json(results);
  });
};

export const updateAccount = (req, res) => {
  const { id } = req.params;
  const { name, description, address, sector, type } = req.body;

  query(
    `UPDATE client_accounts SET name=?, description=?, address=?, sector=?, type=? WHERE id=?`,
    [name, description, address, sector, type, id],
    (err) => {
      if (err) return res.status(500).json({ error: err.message });
      res.json({ message: 'Account updated' });
    }
  );
};

export const deleteAccount = (req, res) => {
  const { id } = req.params;
  query(`DELETE FROM client_accounts WHERE id=?`, [id], (err) => {
    if (err) return res.status(500).json({ error: err.message });
    res.json({ message: 'Account deleted' });
  });
};

export const getAccountById = (req, res) => {
  const { id } = req.params;
  query(`SELECT * FROM client_accounts WHERE id=?`, [id], (err, results) => {
    if (err) return res.status(500).json({ error: err.message });
    if (results.length === 0) return res.status(404).json({ message: 'Account not found' });
    res.json(results[0]);
  });
};
