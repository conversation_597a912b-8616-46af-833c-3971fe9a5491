import fs from "fs"
import path from "path"
import { fileURLToPath } from "url"
import { db } from "../db.js"
import { seedInitialData } from "./seedData.js"

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

export const initializeDatabase = () => {
  return new Promise((resolve, reject) => {
    console.log(" Initializing database...")

    const schemaPath = path.join(__dirname, "schema.sql")

    if (!fs.existsSync(schemaPath)) {
      console.error(" Schema file not found:", schemaPath)
      reject(new Error("Schema file not found"))
      return
    }

    const schema = fs.readFileSync(schemaPath, "utf8")

    const statements = schema
      .split(";")
      .map((stmt) => stmt.trim())
      .filter((stmt) => stmt.length > 0 && !stmt.startsWith("--"))

    console.log(`Found ${statements.length} SQL statements to execute`)

    let completed = 0
    const total = statements.length

    if (total === 0) {
      console.log("No SQL statements to execute")
      resolve()
      return
    }

    statements.forEach((statement, index) => {
      db.query(statement, (err) => {
        if (err) {
          if (err.code === "ER_DB_CREATE_EXISTS" || err.code === "ER_TABLE_EXISTS_ERROR") {
            console.log(`  Statement ${index + 1}: Already exists (skipping)`)
          } else {
            console.error(`Error executing statement ${index + 1}:`, err.message)
            console.error("Statement:", statement.substring(0, 100) + "...")
            reject(err)
            return
          }
        } else {
          console.log(`Statement ${index + 1}: Executed successfully`)
        }

        completed++

        if (completed === total) {
          console.log("Database schema initialization completed")

          seedInitialData()
            .then(() => {
              console.log("Database initialization fully completed")
              resolve()
            })
            .catch((seedErr) => {
              console.error("Error seeding initial data:", seedErr.message)
              reject(seedErr)
            })
        }
      })
    })
  })
}
