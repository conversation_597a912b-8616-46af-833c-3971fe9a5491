import { query } from "../db.js"

export const getFishboneByOpportunity = (req, res) => {
  const { opportunity_id } = req.params

  console.log("📥 Fetching fishbone for opportunity:", opportunity_id)

  query(`SELECT * FROM fishbone_diagrams WHERE opportunity_id = ?`, [opportunity_id], (err, results) => {
    if (err) {
      console.error("❌ Error fetching fishbone:", err.message)
      return res.status(500).json({ error: err.message })
    }

    if (results.length === 0) {
      return res.json({
        opportunity_id: Number.parseInt(opportunity_id),
        departments: [],
        node_placements: [],
      })
    }

    try {
      const fishbone = results[0]
      try {
        const diagramData = JSON.parse(fishbone.diagram_data || "{}")
        fishbone.departments = diagramData.departments || []
        fishbone.node_placements = diagramData.node_placements || []
      } catch (e) {
        fishbone.departments = []
        fishbone.node_placements = []
      }

      console.log("✅ Fishbone found")
      res.json(fishbone)
    } catch (parseError) {
      console.error("❌ Error parsing fishbone data:", parseError.message)
      return res.status(500).json({ error: "Error parsing fishbone data" })
    }
  })
}

export const saveFishbone = (req, res) => {
  const { opportunity_id, departments, node_placements } = req.body

  console.log("📥 Saving fishbone for opportunity:", opportunity_id)
  console.log("Departments:", departments)
  console.log("Node placements:", node_placements)


  const diagramData = JSON.stringify({
    departments: departments || [],
    node_placements: node_placements || [],
  })

  query(`SELECT id FROM fishbone_diagrams WHERE opportunity_id = ?`, [opportunity_id], (err, results) => {
    if (err) {
      console.error("❌ Error checking fishbone existence:", err.message)
      return res.status(500).json({ error: err.message })
    }

    if (results.length === 0) {
      const insertSql = `INSERT INTO fishbone_diagrams (opportunity_id, diagram_data, created_at, updated_at) 
           VALUES (?, ?, NOW(), NOW())`

      console.log("Executing INSERT with params:", [opportunity_id, diagramData])

      query(insertSql, [opportunity_id, diagramData], (insertErr, insertResult) => {
        if (insertErr) {
          console.error("❌ Error creating fishbone:", insertErr.message)
          return res.status(500).json({ error: insertErr.message })
        }
        console.log("✅ Fishbone created successfully with ID:", insertResult.insertId)
        res.json({ message: "✅ Fishbone diagram created successfully", id: insertResult.insertId })
      })
    } else {

      const updateSql = `UPDATE fishbone_diagrams SET diagram_data = ?, updated_at = NOW() WHERE opportunity_id = ?`

      console.log("Executing UPDATE with params:", [diagramData, opportunity_id])

      query(updateSql, [diagramData, opportunity_id], (updateErr) => {
        if (updateErr) {
          console.error("❌ Error updating fishbone:", updateErr.message)
          return res.status(500).json({ error: updateErr.message })
        }
        console.log("✅ Fishbone updated successfully")
        res.json({ message: "✅ Fishbone diagram updated successfully" })
      })
    }
  })
}
