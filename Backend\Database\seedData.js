import { query } from "../db.js"

export const seedInitialData = () => {
  return new Promise((resolve, reject) => {
    query("SELECT COUNT(*) as count FROM admins", (err, results) => {
      if (err) {
        console.error("Error checking existing admins:", err.message)
        reject(err)
        return
      }

      const adminCount = results[0].count

      if (adminCount > 0) {
        console.log("Initial admin data already exists, skipping seed")
        resolve()
        return
      }

      console.log("Seeding initial admin data...")

      const initialAdmins = [
        { username: "super", password: "super123", role: "superadmin" },
        { username: "admin_bfsi", password: "bfsi123", role: "B<PERSON><PERSON>" },
        { username: "admin_defense", password: "defense123", role: "Defense" },
        { username: "admin_telco", password: "telco123", role: "Telco" },
        { username: "admin_enterprise", password: "enterprise123", role: "Enterprise" },
      ]

      let completed = 0
      const total = initialAdmins.length

      initialAdmins.forEach((admin) => {
        const sql = "INSERT INTO admins (username, password, role) VALUES (?, ?, ?)"
        query(sql, [admin.username, admin.password, admin.role], (insertErr) => {
          if (insertErr) {
            console.error(`Error inserting admin ${admin.username}:`, insertErr.message)
            reject(insertErr)
            return
          }

          console.log(`Created admin: ${admin.username} (${admin.role})`)
          completed++

          if (completed === total) {
            console.log("All initial admin data seeded successfully")
            resolve()
          }
        })
      })
    })
  })
}
